import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import { SidebarErrorBoundary } from "@/components/error/ErrorBoundary";
import { SavedPostsList } from "@/components/saved/SavedPostsList";

export default async function SavedPage() {
  await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row">
          {/* Left sidebar - 20% */}
          <div className="w-full lg:w-[20%] mb-5 lg:mb-0">
            <SidebarErrorBoundary>
              <LeftSidebar />
            </SidebarErrorBoundary>
            {/* This is an empty div that takes up the same space as the fixed sidebar */}
            <div className="hidden lg:block h-[calc(100vh-5rem)]"></div>
          </div>

          {/* Gap between left sidebar and main content - 5% */}
          <div className="hidden lg:block lg:w-[5%]"></div>

          {/* Main content - 70% */}
          <div className="w-full lg:w-[70%] space-y-5">
            <h1 className="mb-6 text-2xl font-bold text-gray-900">
              Saved Items
            </h1>

            <SavedPostsList />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
