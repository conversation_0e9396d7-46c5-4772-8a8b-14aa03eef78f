"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Menu, MenuButton, MenuItems, MenuItem } from "@headlessui/react";
import { 
  EllipsisHorizontalIcon, 
  PencilIcon, 
  TrashIcon,
  ExclamationTriangleIcon 
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

interface CommentActionsProps {
  commentId: string;
  commentUserId: string;
  onEdit: () => void;
  onDelete: () => void;
  className?: string;
  size?: 'sm' | 'md';
}

export function CommentActions({ 
  commentId, 
  commentUserId, 
  onEdit, 
  onDelete, 
  className = "",
  size = 'sm'
}: CommentActionsProps) {
  const { data: session } = useSession();
  const [isDeleting, setIsDeleting] = useState(false);

  // Check if user can edit/delete this comment
  const canModify = session?.user && (
    session.user.id === commentUserId ||
    session.user.isAdmin
  );

  // Don't show actions if user can't modify
  if (!canModify) {
    return null;
  }

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this comment? This action cannot be undone.")) {
      return;
    }

    setIsDeleting(true);
    try {
      await onDelete();
    } catch (error) {
      console.error("Error deleting comment:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const iconSize = size === 'sm' ? 'h-3 w-3' : 'h-4 w-4';
  const buttonSize = size === 'sm' ? 'h-6 w-6' : 'h-8 w-8';

  return (
    <Menu as="div" className={cn("relative", className)}>
      <MenuButton 
        className={cn(
          "flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors duration-200 group/menu",
          buttonSize
        )}
        disabled={isDeleting}
      >
        <EllipsisHorizontalIcon 
          className={cn(
            "text-gray-400 group-hover/menu:text-gray-600 transition-colors duration-200",
            iconSize
          )} 
        />
      </MenuButton>

      <MenuItems 
        anchor="bottom end" 
        className="absolute z-30 mt-1 w-40 origin-top-right rounded-lg bg-white py-1 shadow-lg ring-1 ring-black/5 focus:outline-none border border-gray-100"
      >
        <MenuItem>
          <button
            onClick={onEdit}
            className="flex items-center w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200 group/edit"
            disabled={isDeleting}
          >
            <PencilIcon className="h-4 w-4 text-gray-500 mr-2 group-hover/edit:text-blue-600 transition-colors duration-200" />
            Edit
          </button>
        </MenuItem>

        <MenuItem>
          <button
            onClick={handleDelete}
            disabled={isDeleting}
            className="flex items-center w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200 group/delete disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDeleting ? (
              <>
                <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-red-600 border-t-transparent"></div>
                Deleting...
              </>
            ) : (
              <>
                <TrashIcon className="h-4 w-4 text-red-500 mr-2 group-hover/delete:text-red-600 transition-colors duration-200" />
                Delete
              </>
            )}
          </button>
        </MenuItem>

        {/* Admin indicator */}
        {session?.user?.isAdmin && session.user.id !== commentUserId && (
          <div className="px-3 py-1 border-t border-gray-100">
            <div className="flex items-center text-xs text-amber-600">
              <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
              Admin Action
            </div>
          </div>
        )}
      </MenuItems>
    </Menu>
  );
}

// Confirmation Modal Component
interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isDeleting: boolean;
  title?: string;
  message?: string;
}

export function DeleteConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  isDeleting,
  title = "Delete Comment",
  message = "Are you sure you want to delete this comment? This action cannot be undone."
}: DeleteConfirmationModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
          <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
              </div>
              <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                <h3 className="text-base font-semibold leading-6 text-gray-900">
                  {title}
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    {message}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button
              type="button"
              onClick={onConfirm}
              disabled={isDeleting}
              className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto"
            >
              {isDeleting ? (
                <>
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              disabled={isDeleting}
              className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed sm:mt-0 sm:w-auto"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
